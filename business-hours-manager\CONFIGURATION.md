# Business Hours Manager Configuration

## Overview

The Business Hours Manager has been refactored to eliminate redundant configuration arrays and support data attributes for better flexibility.

## Key Improvements

### 1. Simplified Days Configuration

**Before (Confusing & Redundant):**
```javascript
this.DAYS_CONFIG = {
    KEYS: ["sun", "mon", "tue", "wed", "thu", "fri", "sat"],
    NAMES: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
    WEEKDAYS: ["mon", "tue", "wed", "thu", "fri", "sat"],
};
```

**After (Clean & Single Source of Truth):**
```javascript
this.DAYS = [
    { key: "sun", name: "Sunday", isWeekday: false },
    { key: "mon", name: "Monday", isWeekday: true },
    { key: "tue", name: "Tuesday", isWeekday: true },
    { key: "wed", name: "Wednesday", isWeekday: true },
    { key: "thu", name: "Thursday", isWeekday: true },
    { key: "fri", name: "Friday", isWeekday: true },
    { key: "sat", name: "Saturday", isWeekday: false },
];
```

### 2. Configurable Time Slots via Data Attributes

**Default Time Slots:**
The component now supports custom time slots through the `data-time-slots` attribute:

```html
<div id="business-hours" 
     data-time-slots='["06:00","06:30","07:00","07:30","08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00","18:30","19:00","19:30","20:00","20:30","21:00","21:30","22:00","22:30","23:00"]'>
</div>
```

**Custom Time Slots Example:**
```html
<!-- 24-hour format with 15-minute intervals -->
<div id="business-hours" 
     data-time-slots='["08:00","08:15","08:30","08:45","09:00","09:15","09:30","09:45","10:00","10:15","10:30","10:45","11:00","11:15","11:30","11:45","12:00","12:15","12:30","12:45","13:00","13:15","13:30","13:45","14:00","14:15","14:30","14:45","15:00","15:15","15:30","15:45","16:00","16:15","16:30","16:45","17:00","17:15","17:30","17:45","18:00"]'>
</div>
```

## Benefits

1. **Reduced Confusion**: Single array instead of three separate arrays
2. **Better Maintainability**: One place to modify day configuration
3. **Flexibility**: Custom time slots per implementation
4. **Cleaner Code**: Less redundant data structures
5. **Easier Debugging**: Clear relationship between day properties

## Migration Guide

If you're using the old version, no changes are required to your HTML or event listeners. The component maintains the same API and event structure.

### Optional: Add Custom Time Slots

To customize time slots, add the `data-time-slots` attribute to your business hours container:

```html
<!-- Before -->
<div id="business-hours"></div>

<!-- After (with custom time slots) -->
<div id="business-hours" 
     data-time-slots='["09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00"]'>
</div>
```

## Fallback Behavior

- If no `data-time-slots` attribute is provided, the component uses sensible defaults
- If the JSON in `data-time-slots` is invalid, it falls back to defaults with a console warning
- All existing functionality remains unchanged
