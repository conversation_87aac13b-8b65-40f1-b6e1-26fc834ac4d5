// Listen for any business hours changes
document.addEventListener('businessHoursChange', function (event) {
    console.log('✓ Business hours change event received:', event.detail);

    const { businessHours, scannerHours, allData } = event.detail;

    // Update your UI with the new data
    updateBusinessHoursDisplay(businessHours);
    updateScannerHoursDisplay(scannerHours);
});

// document.addEventListener('businessHoursOnlyChange', function (event) {
//     console.log('✓ Business hours only change event received:', event.detail);
// });

// document.addEventListener('scannerHoursOnlyChange', function (event) {
//     console.log('✓ Scanner hours only change event received:', event.detail);
// });
